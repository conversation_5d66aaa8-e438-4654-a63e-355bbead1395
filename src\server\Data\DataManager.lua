-- DataManager.lua
-- Sistema de gestão de dados dos jogadores

local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")

local DataManager = {}

-- DataStore para dados dos jogadores
local playerDataStore = DataStoreService:GetDataStore("PlayerData")

-- Template de dados padrão para novos jogadores
local DEFAULT_DATA = {
    Coins = 500,            -- Moedas iniciais
    Level = 1,              -- Nível do jogador
    Experience = 0,         -- Experiência
    UnlockedTowers = {      -- Torres desbloqueadas
        "BasicTower"        -- Torre inicial
    },
    Stats = {
        GamesPlayed = 0,
        GamesWon = 0,
        TotalKills = 0,
        HighestWave = 0
    },
    Settings = {
        MusicEnabled = true,
        SFXEnabled = true,
        AutoUpgrade = false
    }
}

-- Cache de dados dos jogadores ativos
local playerDataCache = {}

function DataManager:Initialize()
    print("DataManager inicializado")
    
    -- Conectar eventos de jogadores
    Players.PlayerAdded:Connect(function(player)
        self:LoadPlayerData(player)
    end)
    
    Players.PlayerRemoving:Connect(function(player)
        self:SavePlayerData(player)
    end)
end

function DataManager:LoadPlayerData(player)
    local success, data = pcall(function()
        return playerDataStore:GetAsync(player.UserId)
    end)
    
    if success and data then
        playerDataCache[player.UserId] = data
        print("Dados carregados para " .. player.Name)
    else
        -- Criar dados padrão para novo jogador
        playerDataCache[player.UserId] = DEFAULT_DATA
        print("Novos dados criados para " .. player.Name)
    end
end

function DataManager:SavePlayerData(player)
    local data = playerDataCache[player.UserId]
    if not data then return end
    
    local success = pcall(function()
        playerDataStore:SetAsync(player.UserId, data)
    end)
    
    if success then
        print("Dados guardados para " .. player.Name)
    else
        warn("Erro ao guardar dados para " .. player.Name)
    end
    
    -- Limpar cache
    playerDataCache[player.UserId] = nil
end

function DataManager:GetPlayerData(player)
    return playerDataCache[player.UserId]
end

function DataManager:UpdatePlayerData(player, key, value)
    local data = playerDataCache[player.UserId]
    if data then
        data[key] = value
    end
end

function DataManager:AddCoins(player, amount)
    local data = self:GetPlayerData(player)
    if data then
        data.Coins = data.Coins + amount
        return data.Coins
    end
    return 0
end

function DataManager:SpendCoins(player, amount)
    local data = self:GetPlayerData(player)
    if data and data.Coins >= amount then
        data.Coins = data.Coins - amount
        return true
    end
    return false
end

return DataManager
