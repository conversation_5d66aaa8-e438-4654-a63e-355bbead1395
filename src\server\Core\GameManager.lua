-- GameManager.lua
-- Gestor principal do jogo Tower Defense

local GameManager = {}

-- Configurações do jogo
local GAME_CONFIG = {
    MaxPlayers = 6,
    GameDuration = 300, -- 5 minutos
    WaveInterval = 30,  -- 30 segundos entre ondas
}

-- Estados do jogo
local GameState = {
    LOBBY = "Lobby",
    STARTING = "Starting", 
    IN_PROGRESS = "InProgress",
    ENDING = "Ending"
}

-- Variáveis do estado atual
local currentState = GameState.LOBBY
local playersInGame = {}
local currentWave = 0

function GameManager:Initialize()
    print("GameManager inicializado")
    -- Configurar eventos e conexões
end

function GameManager:StartGame()
    if currentState ~= GameState.LOBBY then
        return false
    end
    
    currentState = GameState.STARTING
    print("Jogo a começar...")
    
    -- Lógica para iniciar o jogo
    return true
end

function GameManager:EndGame()
    currentState = GameState.ENDING
    print("Jogo terminado")
    
    -- Lógica para terminar o jogo
    -- Reset para lobby
    currentState = GameState.LOBBY
end

function GameManager:GetGameState()
    return currentState
end

return GameManager
